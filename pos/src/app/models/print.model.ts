import { StorageService } from './../services/storage';
export interface PrintableItem {
  sku: string;
  name?: string;
  quantity: number;
  unit_price: number;  // MRP (Maximum Retail Price)
  sale_price: number;  // Actual selling price
  mrp: number;         // Explicit MRP field (required)
  discount: number;    // Calculated discount (MRP - sale_price)
  total: number;
  taxableAmount?: number;
  gstAmount?: number;
  cgstAmount?: number;
  sgstAmount?: number;
  igstAmount?: number;
  cessAmount?: number;  // CESS amount calculated
  igst?: number;        // IGST percentage
  cgst?: number;        // CGST percentage
  sgst?: number;        // SGST percentage
  cess?: number;        // CESS percentage
  tax?: number;         // Total tax amount
  totalTax?: number;    // Total tax amount calculated
  totalWithTax?: number; // Total amount including tax
  created_at?: string;
}

export interface PrintableAddress {
  full_name: string;
  phone_number: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface PrintableOrder {
  order_id: string;
  customer_name: string;
  customer_id: string;
  facility_name: string;
  total_amount: number;
  created_at?: string;
  address?: PrintableAddress;
  items: PrintableItem[];
  payment_method?: string;
  subtotal: number;
  discount: number;
  grand_total: number;
  copy_of_invoice?: boolean;
}

export interface PrintTotals {
  totalTaxableAmount: number;
  totalCgstAmount: number;
  totalSgstAmount: number;
  totalIgstAmount: number;
  totalGstAmount: number;
  totalCessAmount: number;
  totalQuantity: number;
  totalTax: number;
}

export interface PrintTemplateData extends PrintableOrder {
  currentDate: string;
  totals: PrintTotals;
  copy_of_invoice?: boolean;
  storeAddress: string;
  storePhone: string;
  storeGSTIN: string;
  storeEmail: string;
  created_at?: string;
}