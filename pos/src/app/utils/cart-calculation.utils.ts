import { CartItem, PrintableItem, PrintTotals } from '../models';

export class CartCalculationUtils {

    static calculateSubTotal(cartItems: CartItem[]): number {
      return cartItems.reduce(
        (total, item) => {
          const price = item.mrp || item.selling_price;
          return total + price * item.quantity;
        },
        0,
      );
    }


    static calculateDiscount(cartItems: CartItem[]): number {
      return cartItems.reduce(
        (total, item) => {
          const mrp = item.mrp || 0;
          const sellingPrice = item.selling_price || 0;
          const discountPerUnit = Math.max(0, mrp - sellingPrice);
          return total + (discountPerUnit * item.quantity);
        },
        0,
      );
    }
  
  
    static calculateGrandTotal(cartItems: CartItem[]): number {
      return this.calculateSubTotal(cartItems) - this.calculateDiscount(cartItems);
    }
  
   
    static calculateItemDiscount(item: CartItem): number {
      const mrp = item.mrp || 0;
      const sellingPrice = item.selling_price || 0;
      const discountPerUnit = Math.max(0, mrp - sellingPrice);
      return discountPerUnit * item.quantity;
    }
  
  
    static calculateItemTotal(item: CartItem): number {
      if (item.mrp && item.selling_price){
        const price = item.mrp || item.selling_price;
        const itemSubtotal = price * item.quantity;
        const itemDiscount = this.calculateItemDiscount(item);
        return itemSubtotal - itemDiscount;
      } 
      return item.selling_price * item.quantity;
    }
  
    static formatCurrency(amount: number): string {
        if (amount){
            return `₹${amount.toFixed(2)}`;
        }
        return '₹0.00';
    }

    /**
     * Calculate totals for printable items with tax calculations
     */
    static calculatePrintableTotals(items: PrintableItem[]): PrintTotals {
        let totalTaxableAmount = 0;
        let totalIgstAmount = 0;
        let totalCessAmount = 0;
        let totalQuantity = 0;

        items.forEach(item => {
            // Since sale_price is inclusive of tax, we need to calculate taxable amount
            // Formula: Taxable Amount = Sale Price / (1 + (Total Tax Rate / 100))
            const totalTaxRate = (item.igst || 0) + (item.cess || 0);
            const itemTaxableAmount = totalTaxRate > 0
                ? (item.sale_price * item.quantity) / (1 + (totalTaxRate / 100))
                : item.sale_price * item.quantity;

            totalTaxableAmount += itemTaxableAmount;
            totalQuantity += item.quantity;

            // Store the calculated taxable amount for this item
            item.taxableAmount = itemTaxableAmount;

            // Calculate tax amounts based on taxable amount
            if (item.igst && item.igst > 0) {
                const igstAmount = (itemTaxableAmount * item.igst) / 100;
                totalIgstAmount += igstAmount;
                item.igstAmount = igstAmount;

                // Calculate CGST and SGST for display (IGST split in half)
                const cgstAmount = igstAmount / 2;
                const sgstAmount = igstAmount / 2;
                item.cgstAmount = cgstAmount;
                item.sgstAmount = sgstAmount;
            }

            if (item.cess && item.cess > 0) {
                const cessAmount = (itemTaxableAmount * item.cess) / 100;
                totalCessAmount += cessAmount;
                item.cessAmount = cessAmount;
            }

            // Calculate total tax for this item
            const itemTotalTax = (item.igstAmount || 0) + (item.cessAmount || 0);
            item.totalTax = itemTotalTax;
            item.totalWithTax = itemTaxableAmount + itemTotalTax; // This should equal sale_price * quantity
        });

        // Calculate totals according to GST rules:
        // - IGST split in half gives SGST and CGST for display purposes
        // - Total tax = IGST + CESS
        // - GST equals total tax
        const totalCgstAmount = totalIgstAmount > 0 ? totalIgstAmount / 2 : 0;
        const totalSgstAmount = totalIgstAmount > 0 ? totalIgstAmount / 2 : 0;
        const totalTax = totalIgstAmount + totalCessAmount;
        const totalGstAmount = totalTax; // GST equals total tax

        return {
            totalTaxableAmount,
            totalCgstAmount,
            totalSgstAmount,
            totalIgstAmount,
            totalGstAmount,
            totalCessAmount,
            totalQuantity,
            totalTax
        };
    }
  }