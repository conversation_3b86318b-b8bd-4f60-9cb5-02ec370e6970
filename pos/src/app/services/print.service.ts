import { Injectable } from '@angular/core';
import { PrintTemplateUtil } from '../utils/print-template.util';
import { CartCalculationUtils } from '../utils/cart-calculation.utils';
import { PrintTemplateData, PrintableItem } from '../models/print.model';
import { Order, CartItem } from '../models';
import { TypeSenseService } from './typesense';

interface StoreInfo {
  address: string;
  phone: string;
  gstin: string;
  name: string;
  email: string;
}

interface TaxCalculation {
  taxableAmount: number;
  igstAmount: number;
  cessAmount: number;
  cgstAmount: number;
  sgstAmount: number;
  totalTax: number;
}

@Injectable({
  providedIn: 'root'
})
export class PrintService {
  private static readonly DEFAULT_STORE_INFO: StoreInfo = {
    address: 'New Delhi, India',
    phone: '9667018020',
    gstin: '27AAPC1234D1Z1',
    name: 'ROZANA RURAL COMMERCE PVT LTD',
    email: '<EMAIL>'
  };

  private storeInfoCache: StoreInfo | null = null;

  constructor(private typeSenseService: TypeSenseService) { }

  /**
   * Clear the store info cache (useful when store settings are updated)
   */
  clearStoreCache(): void {
    this.storeInfoCache = null;
  }

  /**
   * Get store information with caching and fallback to default values
   */
  private async getStoreInfo(): Promise<StoreInfo> {
    if (this.storeInfoCache) {
      return this.storeInfoCache;
    }

    try {
      const storeData = await this.typeSenseService.getStoreById();
      this.storeInfoCache = {
        address: storeData?.address || PrintService.DEFAULT_STORE_INFO.address,
        phone: storeData?.phone || PrintService.DEFAULT_STORE_INFO.phone,
        gstin: storeData?.gstin || PrintService.DEFAULT_STORE_INFO.gstin,
        name: storeData?.name || PrintService.DEFAULT_STORE_INFO.name,
        email: storeData?.email || PrintService.DEFAULT_STORE_INFO.email
      };
      return this.storeInfoCache;
    } catch (error) {
      console.error('Error fetching store info, using defaults:', error);
      this.storeInfoCache = { ...PrintService.DEFAULT_STORE_INFO };
      return this.storeInfoCache;
    }
  }

  /**
   * Calculate tax amounts for an item based on selling price and tax rates
   */
  private calculateTaxAmounts(
    sellingPrice: number,
    quantity: number,
    igstRate: number = 0,
    cessRate: number = 0
  ): TaxCalculation {
    const totalTaxRate = igstRate + cessRate;
    const taxableAmount = totalTaxRate > 0
      ? (sellingPrice * quantity) / (1 + (totalTaxRate / 100))
      : sellingPrice * quantity;

    const igstAmount = igstRate > 0 ? (taxableAmount * igstRate) / 100 : 0;
    const cessAmount = cessRate > 0 ? (taxableAmount * cessRate) / 100 : 0;
    const cgstAmount = igstAmount > 0 ? igstAmount / 2 : 0;
    const sgstAmount = igstAmount > 0 ? igstAmount / 2 : 0;
    const totalTax = igstAmount + cessAmount;

    return {
      taxableAmount,
      igstAmount,
      cessAmount,
      cgstAmount,
      sgstAmount,
      totalTax
    };
  }

  /**
   * Convert CartItem to PrintableItem
   */
  private convertCartItemToPrintableItem(cartItem: CartItem): PrintableItem {
    const mrp = cartItem.mrp || cartItem.selling_price;
    const taxCalc = this.calculateTaxAmounts(
      cartItem.selling_price,
      cartItem.quantity,
      cartItem.igst || 0,
      cartItem.cess || 0
    );

    return {
      sku: cartItem.child_sku,
      name: cartItem.name,
      quantity: cartItem.quantity,
      unit_price: mrp,
      sale_price: cartItem.selling_price,
      mrp,
      discount: Math.max(0, mrp - cartItem.selling_price),
      total: cartItem.selling_price * cartItem.quantity,
      cgst: cartItem.cgst,
      sgst: cartItem.sgst,
      igst: cartItem.igst,
      cess: cartItem.cess,
      tax: cartItem.tax,
      igstAmount: taxCalc.igstAmount,
      cessAmount: taxCalc.cessAmount,
      cgstAmount: taxCalc.cgstAmount,
      sgstAmount: taxCalc.sgstAmount,
      totalTax: taxCalc.totalTax,
      totalWithTax: taxCalc.taxableAmount + taxCalc.totalTax
    };
  }

  /**
   * Convert Order to PrintableItem array
   */
  private convertOrderItemsToPrintableItems(order: Order): PrintableItem[] {
    return order.items.map(orderItem => {
      // For orders, assuming all tax is IGST for simplicity
      const igstRate = orderItem.tax || 0;
      const cessRate = 0; // CESS would need to be stored separately

      const taxCalc = this.calculateTaxAmounts(
        orderItem.sale_price,
        orderItem.quantity,
        igstRate,
        cessRate
      );

      return {
        sku: orderItem.sku,
        name: orderItem.name,
        quantity: orderItem.quantity,
        unit_price: orderItem.unit_price,
        sale_price: orderItem.sale_price,
        mrp: orderItem.unit_price,
        discount: Math.max(0, orderItem.unit_price - orderItem.sale_price),
        total: orderItem.sale_price * orderItem.quantity,
        cgst: 0,
        sgst: 0,
        igst: igstRate,
        cess: cessRate,
        tax: orderItem.tax || 0,
        igstAmount: taxCalc.igstAmount,
        cessAmount: taxCalc.cessAmount,
        cgstAmount: taxCalc.cgstAmount,
        sgstAmount: taxCalc.sgstAmount,
        totalTax: taxCalc.totalTax,
        totalWithTax: taxCalc.taxableAmount + taxCalc.totalTax
      };
    });
  }

  /**
   * Generate print template data
   */
  private async generateTemplateData(
    items: PrintableItem[],
    orderData: {
      orderId: string;
      customerName: string;
      customerId?: string;
      paymentMethod: string;
      copyOfInvoice: boolean;
      orderDate?: Date;
      totalAmount?: number;
      subtotalAmount?: number;
      discountAmount?: number;
      facilityName?: string;
      createdAt?: string;
    }
  ): Promise<PrintTemplateData> {
    const totals = CartCalculationUtils.calculatePrintableTotals(items);
    const calculatedTotal = totals.totalTaxableAmount + totals.totalTax;
    const calculatedDiscount = items.reduce((total, item) => total + (item.discount || 0) * item.quantity, 0);
    const storeInfo = await this.getStoreInfo();

    return {
      order_id: orderData.orderId,
      customer_name: orderData.customerName,
      customer_id: orderData.customerId || '',
      facility_name: orderData.facilityName || storeInfo.name,
      total_amount: orderData.totalAmount ?? calculatedTotal,
      items,
      payment_method: orderData.paymentMethod,
      subtotal: orderData.subtotalAmount ?? totals.totalTaxableAmount,
      discount: orderData.discountAmount ?? calculatedDiscount,
      grand_total: orderData.totalAmount ?? calculatedTotal,
      copy_of_invoice: orderData.copyOfInvoice,
      currentDate: (orderData.orderDate || new Date()).toLocaleDateString('en-IN'),
      totals,
      storeAddress: storeInfo.address,
      storePhone: storeInfo.phone,
      storeGSTIN: storeInfo.gstin,
      storeEmail: storeInfo.email,
      created_at: orderData.createdAt
    };
  }

  /**
   * Generate print template for cart items
   */
  async generateCartPrintTemplate(
    cartItems: CartItem[],
    orderId: string,
    customerName?: string,
    paymentMethod: string = 'Cash',
    copyOfInvoice: boolean = false
  ): Promise<string> {
    const printableItems = cartItems.map(item => this.convertCartItemToPrintableItem(item));
    const templateData = await this.generateTemplateData(printableItems, {
      orderId,
      customerName: customerName || '',
      paymentMethod,
      copyOfInvoice
    });
    return PrintTemplateUtil.generateTemplate(templateData);
  }

  /**
   * Generate print template for an order
   */
  async generateOrderPrintTemplate(order: Order, copyOfInvoice: boolean = false): Promise<string> {
    const printableItems = this.convertOrderItemsToPrintableItems(order);
    const templateData = await this.generateTemplateData(printableItems, {
      orderId: order.order_id,
      customerName: order.customer_name,
      customerId: order.customer_id,
      paymentMethod: order.payment?.payment_mode || 'Cash',
      copyOfInvoice,
      orderDate: new Date(order.order_date),
      totalAmount: order.total_amount,
      subtotalAmount: order.subtotal_amount,
      discountAmount: order.discount_amount,
      facilityName: order.facility_name,
      createdAt: order.created_at?.toString()
    });
    return PrintTemplateUtil.generateTemplate(templateData);
  }

  /**
   * Print the generated template
   */
  private printTemplate(htmlTemplate: string): void {
    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        console.error('Failed to open print window. Please check popup blocker settings.');
        return;
      }

      printWindow.document.open();
      printWindow.document.write(htmlTemplate);
      printWindow.document.close();
      printWindow.focus();

      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } catch (error) {
      console.error('Error printing template:', error);
    }
  }

  /**
   * Print cart items
   */
  async printCart(
    cartItems: CartItem[],
    orderId: string,
    customerName?: string,
    paymentMethod: string = 'Cash',
    copyOfInvoice: boolean = false
  ): Promise<void> {
    const template = await this.generateCartPrintTemplate(cartItems, orderId, customerName, paymentMethod, copyOfInvoice);
    this.printTemplate(template);
  }

  /**
   * Print order
   */
  async printOrder(order: Order, copyOfInvoice: boolean = false): Promise<void> {
    const template = await this.generateOrderPrintTemplate(order, copyOfInvoice);
    this.printTemplate(template);
  }
}